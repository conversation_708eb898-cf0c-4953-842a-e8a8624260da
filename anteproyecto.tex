\%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% Plantilla para informe de práctica, DISC, UCN.
% Original por Felipe Narváez, versión modificada por Brian Keith.
% Compilar dos veces, por razones de que el compilador crea un archivo 
% para la tabla de contenido, para que este funcione compilar una vez más.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass[oneside,12pt, letterpaper, titlepage]{book}
% Esto es para poder escribir acentos directamente:
\usepackage[utf8x]{inputenc}
% Esto es para que el LaTeX sepa que el texto esta en español:
\usepackage[spanish,activeacute,es-lcroman]{babel}
% Paquetes de la AMS:
\usepackage{amsmath, amsthm, amsfonts}
%Otros paquetes necesarios.
%\usepackage{apacite}

\usepackage{graphicx}
\usepackage[tc]{titlepic}
\usepackage{fancyhdr}
\usepackage[T1]{fontenc}
\usepackage{titlesec}
\usepackage{float}
%Esto utiliza arial.
\renewcommand{\rmdefault}{phv} % Arial
\renewcommand{\sfdefault}{phv} % Arial
%\usepackage{pslatex}  %Esto utiliza la fuente Times Roman (Casi indistinguible de Times New Roman)
\usepackage[titletoc]{appendix} %Este paquete para los anexos.
\usepackage{setspace} %Para espacio de 1.5
\usepackage{listings}  %Para codigo
\usepackage{multirow} %Tablas con fusiones de fila.
\usepackage{rotating}
\usepackage[numbers]{natbib}
\usepackage[none]{hyphenat} % Evita que las palabras sean cortadas
% Corta los url demasiado largos
\usepackage{url}
\usepackage{breakurl}
\usepackage[breaklinks]{hyperref}
% Para el texto alrededor de la imagen
\usepackage{wrapfig}
\usepackage{subcaption} % para acomodar las imágenes
\usepackage{lscape} %para hoja horizontal
\usepackage[normalem]{ulem} % tachar texto


%--------------------------------------------------------------------------
% Defino un nuevo comando para agregar espacios de 20 puntos, para utilizar
% 20 espacios solo utilizar las palabras \hsp
\newcommand{\hsp}{\hspace{2pt}}
%--------------------------------------------------------------------------
% Defino el nuevo titulo para capítulos con números romanos, como por
% ejemplo:
% I Introducción.
\renewcommand{\thechapter}{\Roman{chapter}}
%--------------------------------------------------------------------------
% Defino el nuevo titulo para subsecciones de capítulos con números árabes
% como por ejemplo:
% 1.1 Antecedentes generales 
\renewcommand{\thesection}{\arabic{chapter}.\arabic{section}}
%--------------------------------------------------------------------------
\addto\captionsspanish{% Replace "english" with the language you use
  \renewcommand{\contentsname}%
    {TABLA DE CONTENIDO}%
}

%--------------------------------------------------------------------------
% Defino el nuevo titulo para figuras de capitulos con numeros arabes
% como por ejemplo:
% 1.1 Antecedentes generales 
\renewcommand{\thefigure}{\arabic{chapter}.\arabic{figure}}
%--------------------------------------------------------------------------
\renewcommand{\thetable}{\arabic{chapter}.\arabic{table}}
%-------------------------------------------------------------------------

\titleformat*{\section}{\fontsize{12}{12}\bfseries}
\titleformat*{\subsection}{\fontsize{12}{12}\bfseries}
\titleformat*{\subsubsection}{\fontsize{12}{12}\bfseries}


%--------------------------------------------------------------------------
%Margenes de pagina.
\usepackage[top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm]{geometry}
%--------------------------------------------------------------------------
%Que aparezca la bibliografía en el índice.
%\usepackage{tocbibind}
%--------------------------------------------------------------------------
%Numeros de pagina en posiciones correctas.
\usepackage{fancyhdr} 
\pagestyle{myheadings}
\renewcommand{\headrulewidth}{0pt}
\fancyhead[L]{}
\fancyhead[R]{\nouppercase{\thepage}}
\fancyfoot[C]{}
%--------------------------------------------------------------------------
\usepackage{afterpage}
\usepackage{textcase}
\usepackage{enumitem}
\setlist{nolistsep}
%Listas ordenadas
\usepackage{datatool}
\newcommand{\sortitem}[1]{%
  \DTLnewrow{list}% Create a new entry
  \DTLnewdbentry{list}{description}{#1}% Add entry as description
}
\newenvironment{sortedlist}{%
  \DTLifdbexists{list}{\DTLcleardb{list}}{\DTLnewdb{list}}% Create new/discard old list
}{%
  \DTLsort{description}{list}% Sort list
  \begin{itemize}[leftmargin=*,label={}]%
    \DTLforeach*{list}{\theDesc=description}{%
      \item \theDesc}% Print each item
  \end{itemize}%
}

%Modificaciones a la tabla de contenidos.
\makeatletter
\def\numberSpaceChapter #1{#1.\enskip}
% I added .\enskip because without these the dots and the space between the number and the title would disappear altogether.

%Esto se encarga que los capítulos tengan puntos en su tab leader.
\renewcommand*\l@chapter[2]{%
  \ifnum \c@tocdepth >\m@ne
    \addpenalty{-\@highpenalty}%
    \vskip 1.0em \@plus\p@
    \setlength\@tempdima{1.5em}%
    \begingroup
      \parindent \z@ \rightskip \@pnumwidth
      \parfillskip -\@pnumwidth
      \leavevmode \bfseries
      \advance\leftskip\@tempdima
      \hskip -\leftskip 
      \@pnum@font #1\nobreak
      \xleaders\hbox{$\m@th
        \mkern \@dotsep mu\hbox{.}\mkern \@dotsep
        mu$}\hfill%
      \nobreak\hb@xt@\@pnumwidth{\hss\@pnum@font #2}\par
      \penalty\@highpenalty
      %Esto le quita la negrita.
    \endgroup
  \fi}
%Capitulos...
\let\orig@chapter\@chapter
\def\@chapter[#1]#2{\ifnum \c@secnumdepth >\m@ne
                       \if@mainmatter
                         \refstepcounter{chapter}%
                         \typeout{\@chapapp\space\thechapter.}%
                         \addcontentsline{toc}{chapter}%
                                   {CAPÍTULO~\protect\numberline{\thechapter}#1}%
                       \else
                         \addcontentsline{toc}{chapter}{#1}%
                       \fi
                    \else
                      \addcontentsline{toc}{chapter}{#1}%
                    \fi
                    \chaptermark{#1}%
                    \addtocontents{lof}{\protect\addvspace{10\p@}}%
                    \addtocontents{lot}{\protect\addvspace{10\p@}}%
                    \if@twocolumn
                      \@topnewpage[\@makechapterhead{#2}]%
                    \else
                      \@makechapterhead{#2}%
                      \@afterheading
                    \fi}
%Esto renueva los comandos asociados a los otros elementos de la tabla de contenidos para que todo quede bien.
\renewcommand*\l@section{\@dottedtocline{1}{1.5em}{2.3em}}
\renewcommand*\l@subsection{\@dottedtocline{2}{3.8em}{3.2em}}
\renewcommand*\l@subsubsection{\@dottedtocline{3}{7.0em}{4.1em}}
\renewcommand*\l@paragraph{\@dottedtocline{4}{10em}{5em}}
\renewcommand*\l@subparagraph{\@dottedtocline{5}{12em}{6em}}
%Esto se encarga de quitarle la negrita a la frontmatter.
\g@addto@macro{\frontmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\g@addto@macro{\mainmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\g@addto@macro{\backmatter}{\addtocontents{toc}{\protect\def\protect\@pnum@font{\normalfont}}}
\makeatother

%You can change the depth to which section numbering occurs, so you can turn it off selectively. By default it is set to 2. If you only want parts, chapters, and sections numbered, not subsections or subsubsections etc., you can change the value of the secnumdepth counter using the \setcounter command, giving the depth level you wish:
\setcounter{secnumdepth}{3}
%A related counter is tocdepth, which specifies what depth to take the Table of Contents to. It can be reset in exactly the same way as secnumdepth. For example:
\setcounter{tocdepth}{3}


%--------------------------------------------------------------------------
% Comienza el documento
\begin{document}
\sloppy %Como \usepackage[none]{hyphenat} evita que las palabras se corten, esta línea realiza la corrección al texto justificado.

\renewcommand{\figurename}{Figura}
\renewcommand{\tablename}{Tabla}
\renewcommand{\listtablename}{ÍNDICE DE TABLAS}
\renewcommand{\listfigurename}{ÍNDICE DE FIGURAS}

\setlength{\parindent}{0pt}
\setlength{\parskip}{\baselineskip} 
\onehalfspace

\captionsetup{belowskip=-24pt}

\begin{titlepage}
\centering
\vspace*{-0.3in}
%--------------------------------------------------------------------------
%Imagen o logotipo de la UCN

\includegraphics[scale=0.35]{./images/u.jpg}\\
\vspace{0.07in}
{\fontsize{14}{14}\selectfont
\textbf{UNIVERSIDAD CATÓLICA DEL NORTE}\\}
{\fontsize{11}{11}\selectfont
\vspace*{0.07in}
FACULTAD DE INGENIERÍA Y CIENCIAS GEOLÓGICAS\\
\vspace*{0.07in}
DEPARTAMENTO DE INGENIERÍA DE SISTEMAS Y COMPUTACIÓN\\
\vspace*{0.07in}
MAGÍSTER EN INGENIERÍA INFORMÁTICA\\}
\vspace*{1.8in}
%--------------------------------------------------------------------------
% Titulo del documento
{\fontsize{14}{14}\bfseries ANÁLISIS Y OPTIMIZACIÓN DE LARGE LANGUAGE MODELS \\
\vspace*{0.07in}
PARA APLICACIONES DE PROCESAMIENTO DE LENGUAJE NATURAL\\}
\vspace*{0.07in}
\vspace*{0.14in}
{\fontsize{12}{12}
\textbf{Anteproyecto de Tesis}\\
}

%Memoria para optar al grado de Licenciado en Ciencias de la Ingeniería y al Titulo de Ingeniero Civil en Computación e Informática\\

%--------------------------------------------------------------------------
\vspace*{1in}
%--------------------------------------------------------------------------
%Datos Personales y/o profesores, alineados a la derecha de la hoja
\begin{center}
%\setlength{\leftskip}{7.2cm}
\fontsize{12}{12}\selectfont
\textbf{DAVID IÑIGO RODRIGO MEZA ASTENGO}\\
\vspace*{0.1in}
Profesor Guía: Doctor Claudio Meneses\\
\end{center}

\vspace*{1.35in}

Antofagasta, Chile\\
\vspace*{0.05in}
\textbf{\today}\\

\end{titlepage}

%--------------------------------------------------------------------------
\titleformat{\chapter}%code
[hang]%shape
{\fontsize{12}{12}\bfseries\centering}%format
{\Roman{chapter}{.}\hsp}%label
{10pt}%sep
{\fontsize{12}{12}\bfseries}%before code
\titlespacing*{\chapter}{0cm}{-13.6pt}{0cm}[0cm]
%--------------------------------------------------------------------------
%Capítulos sin enumeración, número de pagina en romano
\frontmatter
\setcounter{page}{2}
%--------------------------------------------------------------------------
% Esta es la TABLA DE CONTENIDO.
\begin{spacing}{-2}
\tableofcontents
\end{spacing}

\addtocontents{toc}{\let\protect\numberline\protect\numberSpaceChapter}
\addtocontents{toc}{~\hfill\textbf{Página}\par}
%DES-COMENTAR LA LINEA SIGUIENTE SI ES QUE LA TABLA DE CONTENIDOS TIENE MAS DE UNA PAGINA.
%\addtocontents{toc}{\protect\afterpage{~\hfill\textbf{Página}\par\medskip}}
\thispagestyle{plain}

%--------------------------------------------------------------------------
% Esta es la Tabla de Figuras
\begin{spacing}{0}
\listoffigures % Índice de figuras
\end{spacing}
\addtocontents{lof}{~\hfill\textbf{Página}\par}
%\addcontentsline{toc}{chapter}{ÍNDICE DE FIGURAS} % para que aparezca en la tabla de contenidos

%--------------------------------------------------------------------------
% Esta es el índice de Tablas
\begin{spacing}{0}
\listoftables % índice de tablas
\end{spacing}
\addtocontents{lot}{~\hfill\textbf{Página}\par}
%\addcontentsline{toc}{chapter}{ÍNDICE DE TABLAS} % para que aparezca en el índice de contenidos

\chapter*{NOMENCLATURA}
\begin{sortedlist}
\sortitem{\textbf{LLM}: Large Language Model \vspace{6pt}}
\sortitem{\textbf{GPT}: Generative Pre-trained Transformer \vspace{6pt}}
\sortitem{\textbf{BERT}: Bidirectional Encoder Representations from Transformers \vspace{6pt}}
\sortitem{\textbf{NLP}: Natural Language Processing \vspace{6pt}}
\sortitem{\textbf{API}: Application Programming Interface \vspace{6pt}}
\sortitem{\textbf{GPU}: Graphics Processing Unit \vspace{6pt}}
\sortitem{\textbf{TPU}: Tensor Processing Unit \vspace{6pt}}
\sortitem{\textbf{RLHF}: Reinforcement Learning from Human Feedback \vspace{6pt}}
\sortitem{\textbf{RAG}: Retrieval-Augmented Generation \vspace{6pt}}
\sortitem{\textbf{LoRA}: Low-Rank Adaptation \vspace{6pt}}
\sortitem{\textbf{PEFT}: Parameter-Efficient Fine-Tuning \vspace{6pt}}
\end{sortedlist}

%\newpage
\chapter*{GLOSARIO}
\begin{sortedlist}
    \sortitem{\textbf{Large Language Model (LLM):} modelo de inteligencia artificial entrenado en grandes cantidades de texto para comprender y generar lenguaje humano de manera coherente y contextualmente relevante. \vspace{6pt}}
    \sortitem{\textbf{Transformer:} arquitectura de red neuronal que utiliza mecanismos de atención para procesar secuencias de datos, siendo la base de la mayoría de LLMs modernos. \vspace{6pt}}
    \sortitem{\textbf{Fine-tuning:} proceso de ajustar un modelo preentrenado en un conjunto de datos específico para mejorar su rendimiento en tareas particulares. \vspace{6pt}}
    \sortitem{\textbf{Prompt Engineering:} técnica de diseñar y optimizar las instrucciones de entrada para obtener mejores respuestas de los modelos de lenguaje. \vspace{6pt}}
    \sortitem{\textbf{Tokenización:} proceso de dividir el texto en unidades más pequeñas llamadas tokens, que pueden ser palabras, subpalabras o caracteres. \vspace{6pt}}
    \sortitem{\textbf{Embeddings:} representaciones vectoriales densas de palabras o frases que capturan relaciones semánticas en un espacio multidimensional. \vspace{6pt}}
    \sortitem{\textbf{Attention Mechanism:} mecanismo que permite al modelo enfocarse en partes relevantes de la entrada al procesar cada elemento de una secuencia. \vspace{6pt}}
    \sortitem{\textbf{Zero-shot Learning:} capacidad de un modelo para realizar tareas sin haber sido específicamente entrenado en ejemplos de esas tareas. \vspace{6pt}}
    \sortitem{\textbf{Few-shot Learning:} capacidad de un modelo para aprender y realizar tareas con solo unos pocos ejemplos de entrenamiento. \vspace{6pt}}
\end{sortedlist}

%\newpage
\chapter*{RESUMEN}
Los Large Language Models (LLMs) representan uno de los avances más significativos en el campo de la inteligencia artificial y el procesamiento de lenguaje natural en la última década. Estos modelos, basados en arquitecturas Transformer y entrenados en vastas cantidades de texto, han demostrado capacidades extraordinarias para comprender, generar y manipular el lenguaje humano de manera coherente y contextualmente relevante.

El rápido desarrollo y adopción de LLMs como GPT, BERT, LLaMA y otros modelos similares ha transformado múltiples dominios de aplicación, desde la generación de texto y traducción automática hasta la resolución de problemas complejos y la asistencia en tareas de programación. Sin embargo, estos avances también presentan desafíos significativos en términos de eficiencia computacional, interpretabilidad, sesgos inherentes y optimización para tareas específicas.

%Este trabajo de investigación tiene como objetivo realizar un análisis exhaustivo de los Large Language Models, explorando sus fundamentos teóricos, arquitecturas, métodos de entrenamiento y técnicas de optimización. Se pretende investigar enfoques innovadores para mejorar la eficiencia y efectividad de estos modelos, incluyendo técnicas de fine-tuning, métodos de compresión, y estrategias de adaptación para dominios específicos. El estudio abarcará tanto aspectos teóricos como implementaciones prácticas, con el fin de contribuir al entendimiento y mejoramiento de estas tecnologías emergentes en el contexto del procesamiento de lenguaje natural.

%Palabras en inglés en cursiva.
%Utilizar tabla de al final de tesis de doctorado de tesis (solo la parte de opinion mining).

%\paragraph{Palabras claves}
%Minería de opiniones, análisis de sentimientos, métodos híbridos, aprendizaje profundo.
%--------------------------------------------------------------------------
%Capítulos normales con enumeración y con número de pagina árabes
\mainmatter
%--------------------------------------------------------------------------
% Defino el formato de titulo para el capítulo, numero romano y el titulo
% del capítulo separado con una linea de color gris, de esta forma:
% I | Introducción 
\titleformat{\chapter}[hang]
{\fontsize{14}{14}\bfseries}
{CAPÍTULO \Roman{chapter}{.}\hsp}{14pt}{\fontsize{14}{14}\bfseries}
\titlespacing{\chapter}{0cm}{-13.6pt}{0.21cm}[0cm]

\titleformat{\section}[hang]
{\fontsize{14}{14}\bfseries}
{\arabic{chapter}{.}\arabic{section}\hsp}{14pt}{\fontsize{14}{14}\bfseries}
\titlespacing{\section}{0cm}{-0.21cm}{-0.21cm}[0cm]

\titleformat{\subsection}[hang]
{\fontsize{12}{12}\bfseries}
{\arabic{chapter}{.}\arabic{section}{.}\arabic{subsection}\hsp}{12pt}{\fontsize{12}{12}\bfseries}
\titlespacing{\subsection}{0cm}{-0.21cm}{-0.21cm}[0cm] 
%--------------------------------------------------------------------------
% TIP
% Para las secciones pueden probar como
% \section[nombre corto]{Nombre largo}
% El nombre corto aparecerá en el índice de contenido
% el nombre largo aparecerá como titulo de la sección
% Este tip también sirve para el ambiente \chapter.
%--------------------------------------------------------------------------
\chapter[INTRODUCCIÓN]{INTRODUCCIÓN}
El procesamiento de lenguaje natural ha experimentado una revolución sin precedentes con la llegada de los Large Language Models (LLMs). Estos modelos representan un paradigma transformador en la inteligencia artificial, capaces de comprender, generar y manipular el lenguaje humano con una sofisticación que antes parecía inalcanzable. Desde el lanzamiento de modelos pioneros como GPT-3 en 2020, hasta los avances más recientes con GPT-4, Claude, LLaMA y otros modelos de vanguardia, hemos sido testigos de un crecimiento exponencial en las capacidades de estos sistemas.

Los Large Language Models se pueden definir como sistemas de inteligencia artificial entrenados en vastas cantidades de texto para aprender patrones estadísticos del lenguaje humano, permitiéndoles generar texto coherente, responder preguntas, realizar traducciones, escribir código y ejecutar una amplia gama de tareas cognitivas. Estos modelos representan un salto cualitativo en la capacidad de las máquinas para procesar y generar lenguaje natural, superando significativamente a los enfoques tradicionales basados en reglas o modelos más simples.

Los desafíos asociados con los LLMs abarcan múltiples dimensiones: desde aspectos técnicos como la eficiencia computacional, el manejo de contextos largos y la optimización de arquitecturas, hasta consideraciones éticas como los sesgos inherentes, la generación de contenido potencialmente dañino y las implicaciones socioeconómicas de su adopción masiva. Entre las aplicaciones más prominentes de los LLMs se encuentran los asistentes conversacionales, la generación automática de contenido, la programación asistida por IA, la traducción automática avanzada, y el análisis y síntesis de información compleja. Aunque los LLMs han encontrado aplicaciones exitosas en múltiples dominios, su potencial sigue siendo ampliamente inexplorado.

El estudio de los Large Language Models constituye un área de investigación relativamente nueva pero de rápido crecimiento en el campo de la inteligencia artificial y el aprendizaje automático. Aunque existen técnicas establecidas para el procesamiento de lenguaje natural, los LLMs han introducido paradigmas completamente nuevos que requieren enfoques innovadores para su comprensión, optimización y aplicación. Esta disciplina abarca una amplia gama de aspectos técnicos, incluyendo el diseño de arquitecturas, estrategias de entrenamiento, técnicas de fine-tuning, métodos de evaluación, optimización de rendimiento, y consideraciones de implementación práctica. Para abordar estos desafíos de manera efectiva, es fundamental desarrollar marcos teóricos sólidos y metodologías empíricas que permitan caracterizar y mejorar el comportamiento de estos modelos complejos.

El campo de los Large Language Models presenta enormes oportunidades de investigación y desarrollo, especialmente considerando el crecimiento exponencial en la disponibilidad de datos textuales, el aumento en la capacidad computacional, y la demanda creciente de aplicaciones inteligentes de procesamiento de lenguaje. Las aplicaciones potenciales de los LLMs son virtualmente ilimitadas, abarcando desde asistentes personales inteligentes y herramientas de productividad, hasta sistemas de educación personalizada, análisis automatizado de documentos, y generación de contenido creativo. Esta versatilidad es de gran importancia para organizaciones de todos los sectores, ya que permite automatizar tareas complejas que tradicionalmente requerían intervención humana especializada, mejorando la eficiencia operacional y abriendo nuevas posibilidades de innovación.

%El resto de este documento está organizado de la siguiente manera: en el capítulo II se presentan los fundamentos teóricos relacionados con los Large Language Models, incluyendo arquitecturas Transformer, técnicas de entrenamiento y métodos de optimización. El capítulo III expone la motivación detrás de esta investigación, destacando la relevancia y originalidad del trabajo propuesto, así como los resultados esperados del desarrollo de la tesis. El capítulo IV presenta una revisión sistemática del estado del arte en el campo de los LLMs, analizando los avances más recientes y las tendencias emergentes. El capítulo V establece las hipótesis fundamentales que guiarán este trabajo de investigación. En el capítulo VI se especifican los objetivos generales y específicos del proyecto. El capítulo VII detalla la metodología de investigación que se seguirá durante el desarrollo de la tesis. Finalmente, el capítulo VIII presenta el cronograma de trabajo, incluyendo los principales hitos, entregables y actividades planificadas.

\chapter[MARCO TEÓRICO]{MARCO TEÓRICO}
\label{chapter:marco_teorico}
En este capítulo se presenta la base teórica fundamental para la comprensión de los Large Language Models, abordando los conceptos desde los fundamentos más generales hasta los aspectos más específicos y avanzados. En primer lugar, se introducen los conceptos básicos del procesamiento de lenguaje natural y el aprendizaje automático que sustentan el desarrollo de los LLMs. Posteriormente, se explican las arquitecturas Transformer que constituyen la base de los modelos modernos. Finalmente, se abordan las técnicas avanzadas de entrenamiento, fine-tuning y optimización que permiten el desarrollo y mejoramiento de estos modelos complejos.

\section{Fundamentos del procesamiento de lenguaje natural}
Los Large Language Models representan la convergencia de múltiples disciplinas y avances tecnológicos en el campo de la inteligencia artificial \cite{zhao2024survey,bommasani2021opportunities}. Su desarrollo se fundamenta en conceptos provenientes del procesamiento de lenguaje natural, el aprendizaje automático, la lingüística computacional y la ciencia de la computación \cite{qiu2024pretrained,rogers2020primer}. Los desafíos asociados con el desarrollo y optimización de LLMs abarcan desde aspectos teóricos fundamentales hasta consideraciones prácticas de implementación y escalabilidad \cite{kaddour2024challenges}. Para comprender adecuadamente estos modelos complejos, es esencial establecer primero los fundamentos teóricos que sustentan su funcionamiento, comenzando con los principios básicos del procesamiento de lenguaje natural y el aprendizaje automático.

\subsection{Aprendizaje automático y redes neuronales profundas}
En la era actual, la disponibilidad masiva de datos textuales y el incremento exponencial en la capacidad computacional han creado las condiciones ideales para el desarrollo de modelos de aprendizaje automático cada vez más sofisticados \cite{kaplan2020scaling}. Los Large Language Models representan la culminación de décadas de investigación en aprendizaje automático, aprovechando vastas cantidades de texto para aprender representaciones complejas del lenguaje humano \cite{qiu2024pretrained,wei2022emergent}. El objetivo fundamental del aprendizaje automático en este contexto es desarrollar sistemas capaces de comprender y generar lenguaje natural de manera coherente y contextualmente apropiada, como se evidencia en los avances recientes con modelos como GPT-4 \cite{openai2024gpt4}, Claude 3 \cite{anthropic2024claude3}, Gemini \cite{team2023gemini}, y LLaMA 3 \cite{dubey2024llama3}.

Este trabajo se enfoca en el estudio y análisis de los LLMs, los cuales constituyen un paradigma avanzado de aprendizaje automático. En el contexto de estos modelos, se tiene una entrada textual $X$ (como un prompt o consulta), y el objetivo es generar una salida $Y$ (como una respuesta, traducción, o continuación del texto) que sea coherente, relevante y útil. El proceso de aprendizaje implica entrenar el modelo para capturar patrones estadísticos complejos en el lenguaje, permitiendo la generalización a nuevas instancias y contextos no vistos durante el entrenamiento.

Los LLMs se basan principalmente en arquitecturas de aprendizaje profundo, específicamente en redes neuronales Transformer \cite{vaswani2017attention} que utilizan mecanismos de atención para procesar secuencias de texto. Estos modelos pueden clasificarse según diferentes paradigmas de aprendizaje: aprendizaje auto-supervisado durante el pre-entrenamiento \cite{qiu2020pretrained}, aprendizaje supervisado durante el fine-tuning \cite{li2024fine}, y aprendizaje por refuerzo a partir de retroalimentación humana (RLHF) \cite{ouyang2022training,wu2025sailing} para alineación y mejora del comportamiento. Los desarrollos más recientes incluyen técnicas avanzadas como Constitutional AI \cite{bai2022constitutional}, métodos de fine-tuning eficientes \cite{hu2024lora}.

\subsection{Procesamiento masivo de texto y representaciones vectoriales}
La cantidad de información textual disponible en formato digital ha crecido exponencialmente en las últimas décadas. Estimaciones actuales sugieren que la cantidad de texto disponible en internet supera los petabytes, incluyendo páginas web, documentos académicos, libros digitalizados, redes sociales, foros, y una multitud de otras fuentes textuales. Este crecimiento masivo ha creado tanto oportunidades como desafíos para el desarrollo de sistemas inteligentes capaces de procesar y comprender este vasto corpus de conocimiento humano.

Los Large Language Models abordan este desafío de manera fundamentalmente diferente a los enfoques tradicionales de minería de texto \cite{fanni2024journey,wan2024efficient}. Mientras que los métodos clásicos se enfocan en extraer características específicas y aplicar algoritmos de aprendizaje automático tradicionales, los LLMs aprenden representaciones distribuidas del lenguaje que capturan relaciones semánticas y sintácticas complejas de manera implícita.

Los LLMs presentan características distintivas que los diferencian de los enfoques tradicionales de procesamiento de texto:
\begin{itemize}
\item Capacidad para procesar y generar texto de manera end-to-end sin requerir ingeniería manual de características.
\item Habilidad para capturar dependencias a largo plazo y relaciones contextuales complejas en el texto.
\item Flexibilidad para adaptarse a múltiples tareas de procesamiento de lenguaje natural sin modificaciones arquitectónicas significativas.
\item Capacidad emergente para realizar tareas no vistas durante el entrenamiento a través de few-shot y zero-shot learning.
\end{itemize}

Los objetivos principales en el estudio de LLMs incluyen: comprensión del lenguaje natural, generación de texto coherente, traducción automática, resumen de documentos, respuesta a preguntas, análisis de sentimientos, y una amplia gama de tareas cognitivas complejas. En este trabajo se realizará un análisis comprehensivo de estas capacidades y los mecanismos subyacentes que las hacen posibles.

\subsection{Large Language Models: definición y características fundamentales}
El desarrollo de Large Language Models presenta desafíos únicos y oportunidades sin precedentes en el campo del procesamiento de lenguaje natural. Estos modelos se caracterizan por su capacidad para procesar y generar texto de manera coherente y contextualmente relevante, aprovechando patrones estadísticos aprendidos de vastas cantidades de datos textuales. Los LLMs han demostrado capacidades emergentes que van más allá de las tareas específicas para las que fueron entrenados, mostrando habilidades de razonamiento, creatividad y adaptación que antes se consideraban exclusivamente humanas.

Un Large Language Model puede ser formalmente definido como una función probabilística $P(w_t | w_1, w_2, ..., w_{t-1})$ que predice la probabilidad de la siguiente palabra $w_t$ dado un contexto de palabras anteriores. Sin embargo, esta definición simple no captura la complejidad real de estos sistemas, que incorporan mecanismos sofisticados de atención \cite{vaswani2017attention} y arquitecturas que permiten el procesamiento paralelo eficiente de secuencias largas de texto. Los avances recientes han demostrado capacidades emergentes \cite{wei2022emergent} y han establecido leyes de escalamiento \cite{hoffmann2022training} que guían el desarrollo de modelos cada vez más capaces.

Los LLMs modernos se caracterizan por varios aspectos fundamentales que los distinguen de enfoques anteriores. Primero, su escala masiva, con modelos que contienen desde cientos de millones hasta billones de parámetros, siguiendo las leyes de escalamiento establecidas \cite{kaplan2020scaling}. Segundo, su capacidad de generalización, permitiendo realizar tareas no vistas durante el entrenamiento mediante técnicas como few-shot y zero-shot learning \cite{brown2020language,dong2024survey}. Tercero, su naturaleza emergente, donde capacidades complejas surgen de la interacción de componentes más simples a gran escala \cite{wei2022emergent}. Finalmente, su versatilidad, siendo capaces de abordar una amplia gama de tareas de procesamiento de lenguaje natural sin modificaciones arquitecturales específicas, como se demuestra en aplicaciones especializadas \cite{yang2025specialized,xiao2024comprehensive} y técnicas avanzadas de prompting \cite{liu2024systematic}.

% Incluir el contenido detallado sobre LLMs desde el archivo separado
\input{marco_teorico_llms}








% Incluir capítulos adicionales
%\input{objetivos}
%\input{metodologia}
%\input{cronograma}



%--------------------------------------------------------------------------
% BIBLIOGRAFÍA
%--------------------------------------------------------------------------

\renewcommand\bibname{BIBLIOGRAFÍA}
\bibliographystyle{IEEEtran}
%\bibliographystyle{apacite}
\addcontentsline{toc}{chapter}{BIBLIOGRAFÍA}
\bibliography{references}{}

%\def\bibindent{1cm}
%\begin{thebibliography}{99\kern\bibindent}%Si tienes más de 99 editar este número a la cantidad de referencias que tengas ;)
%\makeatletter
%\let\old@biblabel\@biblabel
%\def\@biblabel#1{{}\kern\bibindent} %En el { } agregar \old@biblabel{#1}
%\let\old@bibitem\bibitem
%\def\bibitem#1{\old@bibitem{#1}\leavevmode\kern-\bibindent\kern-2.1em}
%\makeatother
    

%    \bibitem{1} Law, A. M. y Kelton W. D. (2000). \textit{Simulation Modeling and Analysis} (3 edition). Boston, USA: McGraw-Hill.

%\end{thebibliography}



%\mainmatter %Esto hace que los anexos SI tengan números de cap.
%Se utilizan letras en vez de números.
\titleformat{\chapter}[hang]
{\fontsize{12}{12}\bfseries}
{ANEXO\hsp\Alph{chapter}{.}\hsp}{0pt}{\fontsize{12}{12}\bfseries}
\titlespacing{\chapter}{0cm}{-13.6pt}{0.21cm}[0cm]

%Se utilizan letras en vez de numeros.
\renewcommand{\thesection}{\Alph{chapter}.\arabic{section}}
\renewcommand{\thefigure}{\Alph{chapter}.\arabic{figure}}
\renewcommand{\thetable}{\Alph{chapter}.\arabic{table}}


%--------------------------------------------------------------------------
\end{document}