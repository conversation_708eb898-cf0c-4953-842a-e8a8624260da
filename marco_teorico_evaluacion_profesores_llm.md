# <PERSON>: Evaluación de Profesores Universitarios mediante Modelos de Lenguaje de Gran Escala (LLMs)

## 1. Introducción

La evaluación de profesores universitarios ha sido tradicionalmente un proceso manual que depende de cuestionarios de opción múltiple completados por estudiantes al final de cada curso, complementados con secciones de escritura libre opcional. Este proceso, conocido como Student Evaluation of Teaching (SET), posteriormente requiere evaluación humana para interpretar los comentarios cualitativos. La presente investigación propone explorar el potencial de los Modelos de Lenguaje de Gran Escala (Large Language Models - LLMs) para automatizar y mejorar este proceso evaluativo, comparando diferentes agentes LLM entre sí y con la evaluación humana tradicional.

## 2. Estado del Arte: Evaluaciones de Profesores mediante LLMs

### 2.1 Análisis de Sentimientos en Evaluaciones Estudiantiles

**Peña-Torres (2024)** presenta uno de los trabajos más relevantes en el área, desarrollando un enfoque para analizar sentimientos expresados en comentarios SET utilizando LLMs. Su investigación, publicada en *Ingeniería y Competitividad*, demuestra la efectividad de los LLMs en el análisis de sentimientos de comentarios estudiantiles, resaltando su potencial para mejorar el proceso de evaluación docente.

**Metodología del estudio:**
- Dataset de 365 comentarios en español recolectados entre 2018-2023
- Uso de tres modelos pre-entrenados: Py-sentimiento, VADER personalizado, y DistilBERT
- Evaluación mediante crowdsourcing para establecer línea base
- Desarrollo de herramienta prototipo (ASETool) con arquitectura de microservicios

**Resultados principales:**
- DistilBERT alcanzó 93% de precisión en clasificación de polaridad
- Py-sentimiento obtuvo 85% de precisión
- VADER logró 79% de precisión
- Los comentarios negativos tienden a ser más largos que los positivos

### 2.2 Frameworks Multi-Agente para Evaluación Educativa

**Chen et al. (2025)** introducen MAJ-Eval (Multi-Agent-as-Judge), un framework innovador que utiliza múltiples agentes LLM para simular evaluaciones multi-dimensionales humanas. Aunque no se enfoca específicamente en evaluación docente, su metodología es altamente relevante para el contexto educativo.

**Características del framework MAJ-Eval:**
- Creación automática de personas de stakeholders desde documentos de dominio específico
- Debate grupal entre agentes para refinamiento de evaluaciones
- Evaluación en dos dominios: generación de preguntas-respuestas para niños y resúmenes médicos
- Superior alineación con evaluaciones humanas comparado con métricas tradicionales

### 2.3 Evaluación Automatizada en Educación Superior

**Investigaciones recientes (2024-2025)** muestran un creciente interés en la aplicación de IA para evaluación educativa:

- **Automated Grading Systems**: Desarrollo de sistemas de calificación automatizada que revolucionan la evaluación de trabajos estudiantiles (OSU, 2025)
- **LLM-as-Judge Paradigm**: Emergencia del paradigma donde LLMs actúan como evaluadores sustitutos de humanos (Zheng et al., 2023)
- **Multi-Agent Evaluation**: Sistemas que emplean múltiples agentes LLM con roles específicos para evaluación colaborativa

## 3. Fundamentos Teóricos

### 3.1 Student Evaluation of Teaching (SET)

El SET ha sido durante un siglo el método estándar para evaluar la efectividad docente en instituciones de educación superior. Los sistemas modernos incorporan:

- **Escalas Likert**: Para evaluaciones cuantitativas estructuradas
- **Preguntas numéricas**: Para métricas específicas de desempeño
- **Preguntas abiertas**: Para retroalimentación cualitativa detallada

**Limitaciones del SET tradicional:**
- Análisis manual intensivo en tiempo y recursos
- Subjetividad en interpretación de comentarios cualitativos
- Inconsistencia entre evaluadores humanos
- Dificultad para procesar grandes volúmenes de datos

### 3.2 Modelos de Lenguaje de Gran Escala (LLMs)

Los LLMs representan un avance significativo en procesamiento de lenguaje natural, con capacidades para:

- **Análisis de sentimientos**: Clasificación automática de polaridad emocional
- **Comprensión contextual**: Interpretación de significados implícitos
- **Generación de texto**: Producción de retroalimentación estructurada
- **Razonamiento multi-dimensional**: Evaluación según múltiples criterios

**Modelos relevantes identificados:**
- **GPT-4**: Modelo generativo con capacidades avanzadas de razonamiento
- **BERT y variantes**: Modelos bidireccionales para comprensión contextual
- **RoBERTa**: Optimización robusta de BERT para tareas específicas
- **DistilBERT**: Versión ligera y eficiente de BERT

### 3.3 Paradigma LLM-as-a-Judge

Este paradigma emergente utiliza LLMs como evaluadores sustitutos de humanos, ofreciendo:

**Ventajas:**
- Escalabilidad para grandes volúmenes de datos
- Consistencia en criterios de evaluación
- Reducción de costos y tiempo
- Capacidad de procesamiento 24/7

**Desafíos:**
- Sesgo inherente del modelo
- Limitaciones en comprensión contextual específica
- Necesidad de validación contra evaluaciones humanas
- Consideraciones éticas y de transparencia

## 4. Metodología Propuesta para la Investigación

### 4.1 Enfoque Multi-Agente

La investigación propone implementar un sistema multi-agente donde diferentes LLMs actúan como evaluadores independientes:

**Agentes propuestos:**
- **Agente Pedagógico**: Enfocado en metodología de enseñanza
- **Agente de Contenido**: Evaluación de dominio disciplinar
- **Agente de Comunicación**: Análisis de claridad y efectividad comunicativa
- **Agente de Engagement**: Evaluación de participación estudiantil

### 4.2 Comparación Multi-Dimensional

**Dimensiones de evaluación:**
1. **Precisión**: Concordancia con evaluaciones humanas
2. **Consistencia**: Estabilidad entre evaluaciones repetidas
3. **Eficiencia**: Tiempo y recursos computacionales
4. **Interpretabilidad**: Claridad de justificaciones proporcionadas

### 4.3 Validación Experimental

**Diseño experimental propuesto:**
- Comparación entre múltiples LLMs (GPT-4, Claude, Gemini, etc.)
- Evaluación cruzada con evaluadores humanos expertos
- Análisis de concordancia inter-evaluador
- Estudio de casos específicos por disciplina

## 5. Implicaciones y Contribuciones Esperadas

### 5.1 Contribuciones Teóricas

- Desarrollo de framework teórico para evaluación docente automatizada
- Identificación de dimensiones críticas en evaluación multi-agente
- Establecimiento de métricas de validación específicas para contexto educativo

### 5.2 Contribuciones Prácticas

- Sistema prototipo para evaluación automatizada de profesores
- Reducción significativa en tiempo de procesamiento de evaluaciones
- Mejora en objetividad y consistencia evaluativa
- Herramientas de apoyo para toma de decisiones administrativas

### 5.3 Contribuciones Metodológicas

- Protocolo estandarizado para implementación de LLMs en evaluación educativa
- Métricas de validación específicas para contexto universitario
- Framework de comparación entre diferentes arquitecturas de LLM

## 6. Consideraciones Éticas y Limitaciones

### 6.1 Aspectos Éticos

- **Transparencia**: Necesidad de explicabilidad en decisiones automatizadas
- **Equidad**: Prevención de sesgos discriminatorios
- **Privacidad**: Protección de datos estudiantiles y docentes
- **Autonomía**: Mantenimiento del juicio humano en decisiones críticas

### 6.2 Limitaciones Identificadas

- Dependencia de calidad de datos de entrenamiento
- Posible pérdida de matices contextuales específicos
- Necesidad de actualización continua de modelos
- Resistencia al cambio en comunidad académica

## 7. Conclusiones Preliminares

La literatura revisada sugiere un potencial significativo para la aplicación de LLMs en evaluación docente universitaria. Los estudios pioneros de Peña-Torres (2024) y los avances en frameworks multi-agente como MAJ-Eval proporcionan bases sólidas para el desarrollo de sistemas más sofisticados y precisos.

La investigación propuesta contribuirá al avance del conocimiento en la intersección de inteligencia artificial y evaluación educativa, proporcionando evidencia empírica sobre la viabilidad y efectividad de sistemas automatizados para mejorar los procesos evaluativos tradicionales en educación superior.

## 8. Trabajos Relacionados Adicionales

### 8.1 Análisis de Sentimientos en Contextos Educativos

**Giang et al. (2020)** desarrollaron un sistema de análisis de sentimientos para retroalimentación estudiantil universitaria, utilizando técnicas de procesamiento de lenguaje natural y aprendizaje automático. Su trabajo demostró la viabilidad de automatizar el análisis de comentarios estudiantiles para mejorar la calidad educativa.

**Dalipi et al. (2021)** realizaron una revisión sistemática del análisis de sentimientos en retroalimentación estudiantil en MOOCs, identificando tendencias y desafíos en la aplicación de estas técnicas en entornos educativos masivos.

**Neumann & Linzmayer (2021)** implementaron un enfoque de análisis de sentimientos para capturar retroalimentación y emociones estudiantiles en cursos de computación de gran escala, demostrando mejoras en la comprensión del engagement estudiantil.

### 8.2 Evaluación Automatizada mediante IA

**Ren et al. (2023)** propusieron un sistema de puntuación automática para retroalimentación estudiantil en evaluación docente basado en análisis de sentimientos a nivel de aspecto, logrando identificar dimensiones específicas de desempeño docente.

**Katragadda et al. (2020)** realizaron un análisis de rendimiento de algoritmos de aprendizaje automático para retroalimentación estudiantil, comparando Support Vector Machines, Naive Bayes y otros enfoques tradicionales.

**Reddy et al. (2022)** utilizaron redes neuronales Long Short-Term Memory (LSTM) para analizar reseñas estudiantiles sobre desempeño docente, demostrando superior capacidad de captura de dependencias temporales en texto.

### 8.3 Frameworks de Evaluación Multi-Agente

**Li et al. (2024)** desarrollaron MatEval, un framework de discusión multi-agente para evaluación de texto de extremo abierto, demostrando mejoras significativas en la calidad evaluativa mediante colaboración entre agentes.

**Liang et al. (2024)** exploraron el fomento del pensamiento divergente en LLMs mediante debate multi-agente, estableciendo principios fundamentales para la implementación de sistemas colaborativos de evaluación.

**Koupaee et al. (2025)** presentaron MADISSE, un framework que enmarca la evaluación como debate entre agentes con posturas iniciales opuestas, mejorando la robustez de las evaluaciones finales.

## 9. Gaps en la Literatura y Oportunidades de Investigación

### 9.1 Limitaciones de Estudios Existentes

**Escasez de estudios específicos en evaluación docente:**
- La mayoría de investigaciones se enfocan en evaluación de contenido estudiantil
- Pocos estudios abordan específicamente la evaluación de desempeño docente
- Falta de datasets etiquetados específicos para evaluación de profesores

**Limitaciones metodológicas:**
- Estudios principalmente en inglés, con limitada investigación en español
- Datasets pequeños (típicamente <1000 muestras)
- Falta de validación longitudinal de sistemas implementados

**Ausencia de comparaciones sistemáticas:**
- No existen estudios que comparen múltiples LLMs en evaluación docente
- Falta de benchmarks estandarizados para el dominio
- Ausencia de métricas específicas para contexto educativo universitario

### 9.2 Oportunidades Identificadas

**Desarrollo de sistemas multi-agente especializados:**
- Implementación de agentes con roles específicos (pedagógico, disciplinar, comunicativo)
- Exploración de arquitecturas de debate y consenso entre agentes
- Desarrollo de mecanismos de agregación de evaluaciones multi-dimensionales

**Validación cross-cultural y multilingüe:**
- Estudios en contextos educativos latinoamericanos
- Comparación de efectividad entre idiomas
- Adaptación cultural de criterios evaluativos

**Integración con sistemas institucionales:**
- Desarrollo de APIs para integración con plataformas LMS
- Implementación de dashboards para administradores académicos
- Sistemas de retroalimentación en tiempo real para docentes

## 10. Marco Conceptual Propuesto

### 10.1 Modelo Teórico Multi-Dimensional

La investigación propone un modelo teórico que integra múltiples dimensiones evaluativas:

**Dimensión Pedagógica:**
- Claridad en explicaciones
- Organización del contenido
- Metodologías de enseñanza
- Adaptación a diferentes estilos de aprendizaje

**Dimensión Comunicativa:**
- Efectividad en comunicación oral y escrita
- Capacidad de respuesta a preguntas
- Feedback constructivo a estudiantes
- Accesibilidad y disponibilidad

**Dimensión Disciplinar:**
- Dominio del contenido
- Actualización en el campo
- Conexión teoría-práctica
- Relevancia del material presentado

**Dimensión Motivacional:**
- Capacidad de generar interés
- Fomento de participación estudiantil
- Creación de ambiente de aprendizaje positivo
- Inspiración para aprendizaje continuo

### 10.2 Arquitectura del Sistema Propuesto

**Capa de Ingesta de Datos:**
- Procesamiento de comentarios de texto libre
- Normalización y limpieza de datos
- Extracción de características relevantes
- Anonimización para protección de privacidad

**Capa de Procesamiento Multi-Agente:**
- Agente Especialista Pedagógico (basado en GPT-4/Claude)
- Agente Especialista Disciplinar (basado en modelos específicos de dominio)
- Agente Especialista Comunicativo (basado en modelos de análisis de discurso)
- Agente Coordinador (para síntesis y resolución de conflictos)

**Capa de Síntesis y Reporte:**
- Agregación ponderada de evaluaciones
- Generación de reportes explicables
- Identificación de áreas de mejora
- Comparación con benchmarks institucionales

---

**Referencias Bibliográficas Completas:**

1. Chen, J., Lu, Y., Wang, X., Zeng, H., Huang, J., Gesi, J., ... & Wang, D. (2025). Multi-Agent-as-Judge: Aligning LLM-Agent-Based Automated Evaluation with Multi-Dimensional Human Evaluation. *arXiv preprint arXiv:2507.21028*.

2. Dalipi, F., Zdravkova, K., & Ahlgren, F. (2021). Sentiment analysis of students' feedback in MOOCs: A systematic literature review. *Frontiers in Artificial Intelligence*, 4, 728708.

3. Giang, N. T. P., Dien, T. T., & Khoa, T. T. M. (2020). Sentiment analysis for university students' feedback. In *Advances in Information and Communication: Proceedings of the 2020 Future of Information and Communication Conference (FICC)* (pp. 55-66). Springer.

4. Katragadda, S., Ravi, V., Kumar, P., & Lakshmi, G. J. (2020). Performance analysis on student feedback using machine learning algorithms. In *2020 6th international conference on advanced computing and communication systems (ICACCS)* (pp. 1161-1163). IEEE.

5. Koupaee, M., Vincent, J. W., Mansour, S., Shalyminov, I., He, H., Song, H., ... & Su, H. (2025). Faithful, unfaithful or ambiguous? multi-agent debate with initial stance for summary evaluation. In *Proceedings of the 2025 Conference of the Nations of the Americas Chapter of the Association for Computational Linguistics: Human Language Technologies* (pp. 12209-12246).

6. Li, Y., Zhang, S., Wu, R., Huang, X., Chen, Y., Xu, W., ... & Min, D. (2024). Mateval: A multi-agent discussion framework for advancing open-ended text evaluation. In *International Conference on Database Systems for Advanced Applications* (pp. 415-426). Springer.

7. Liang, T., He, Z., Jiao, W., Wang, X., Wang, Y., Wang, R., ... & Tu, Z. (2024). Encouraging divergent thinking in large language models through multi-agent debate. In *Proceedings of the 2024 Conference on Empirical Methods in Natural Language Processing* (pp. 17889-17904).

8. Neumann, M., & Linzmayer, R. (2021). Capturing student feedback and emotions in large computing courses: A sentiment analysis approach. In *Proceedings of the 52nd ACM Technical Symposium on Computer Science Education* (pp. 541-547).

9. Peña-Torres, J. A. (2024). Towards an improved of teaching practice using Sentiment Analysis in Student Evaluation. *Ingeniería y Competitividad*, 26(2), e-21013759. https://doi.org/10.25100/iyc.v26i2.13759

10. Reddy, S. S., Gadiraju, M., & Maheswara Rao, V. (2022). Analyzing student reviews on teacher performance using long short-term memory. In *Innovative Data Communication Technologies and Application: Proceedings of ICIDCA 2021* (pp. 539-553). Springer.

11. Ren, P., Yang, L., & Luo, F. (2023). Automatic scoring of student feedback for teaching evaluation based on aspect-level sentiment analysis. *Education and Information Technologies*, 28(1), 797-814.
